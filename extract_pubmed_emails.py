#!/usr/bin/env python3
"""
PubMed Email Extractor

This script extracts author names and email addresses from PubMed data files.
It processes the structured format and creates a CSV output with Author Name and Email columns.

Author: AI Assistant
Date: 2025-09-17
"""

import os
import re
import pandas as pd
import warnings
import glob
import argparse
from datetime import datetime

# Import rich_progress for gradient progress bars
try:
    import rich_progress
    HAS_RICH = True
except ImportError:
    HAS_RICH = False
    print("Note: rich_progress not available. Using basic progress indicators.")

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

def print_status(message, status_type="info"):
    """Print status message with or without rich formatting."""
    if HAS_RICH:
        rich_progress.print_status(message, status_type)
    else:
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")

def extract_emails_from_pubmed(file_path):
    """
    Extract author names and email addresses from PubMed data file.

    Args:
        file_path (str): Path to the PubMed data file

    Returns:
        pandas.DataFrame: DataFrame with Author Name and Email columns
    """
    print_status("Starting PubMed email extraction", "header")
    print_status(f"Processing file: {file_path}", "info")

    # Check if file exists
    if not os.path.exists(file_path):
        print_status(f"Error: File not found: {file_path}", "error")
        return pd.DataFrame(columns=['Author Name', 'Email'])

    # Read the file
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='latin-1') as file:
                lines = file.readlines()
        except Exception as e:
            print_status(f"Error reading file: {str(e)}", "error")
            return pd.DataFrame(columns=['Author Name', 'Email'])

    # Initialize variables
    extracted_data = []
    current_author_full = None

    # Email pattern
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'

    print_status(f"Processing {len(lines)} lines", "info")

    # Process each line
    for i, line in enumerate(lines):
        line = line.strip()

        # Check for Full Author Name (FAU)
        if line.startswith('FAU - '):
            current_author_full = line[6:].strip()  # Remove 'FAU - ' prefix

        # Check for any line that contains an email address
        emails = re.findall(email_pattern, line)

        if emails and current_author_full:
            for email in emails:
                # Clean up author name (remove all commas and extra spaces)
                author_name = current_author_full.strip().replace(',', '').strip()

                extracted_data.append({
                    'Author Name': author_name,
                    'Email': email.lower()  # Convert email to lowercase
                })

                print_status(f"Found: {author_name} - {email}", "success")

        # Reset author info when we encounter a new record (PMID)
        if line.startswith('PMID- '):
            current_author_full = None
    
    # Create DataFrame
    df = pd.DataFrame(extracted_data)
    
    if len(df) > 0:
        # Remove duplicates based on email address
        df_unique = df.drop_duplicates(subset=['Email'], keep='first')
        
        print_status(f"Total records found: {len(df)}", "info")
        print_status(f"Unique emails: {len(df_unique)}", "info")
        
        return df_unique
    else:
        print_status("No email addresses found in the file", "warning")
        return pd.DataFrame(columns=['Author Name', 'Email'])

def process_directory(directory_path):
    """
    Process all PubMed files in a directory and extract emails.

    Args:
        directory_path (str): Path to directory containing PubMed files

    Returns:
        pandas.DataFrame: Combined DataFrame with all extracted emails
    """
    print_status(f"Processing directory: {directory_path}", "header")

    # Find all .txt files in the directory
    txt_files = glob.glob(os.path.join(directory_path, "*.txt"))

    if not txt_files:
        print_status(f"No .txt files found in directory: {directory_path}", "warning")
        return pd.DataFrame(columns=['Author Name', 'Email'])

    print_status(f"Found {len(txt_files)} .txt files to process", "info")

    all_emails = []

    for file_path in txt_files:
        print_status(f"Processing file: {os.path.basename(file_path)}", "info")
        df_file = extract_emails_from_pubmed(file_path)
        if len(df_file) > 0:
            all_emails.append(df_file)

    if all_emails:
        # Combine all DataFrames
        combined_df = pd.concat(all_emails, ignore_index=True)
        # Remove duplicates based on email
        combined_df = combined_df.drop_duplicates(subset=['Email'], keep='first')
        return combined_df
    else:
        return pd.DataFrame(columns=['Author Name', 'Email'])

def main():
    """Main function to run the email extraction process."""

    # Set up command line argument parsing
    parser = argparse.ArgumentParser(description='Extract emails and author names from PubMed data files')
    parser.add_argument('--directory', '-d',
                       help='Directory path containing PubMed .txt files (if not provided, will prompt for input)')
    parser.add_argument('--output', '-o',
                       default='output',
                       help='Output directory path (default: output)')

    args = parser.parse_args()

    # Get directory path
    if args.directory:
        directory_path = args.directory
    else:
        # Prompt user for directory path
        directory_path = input("Enter the directory path containing PubMed files: ").strip()
        if not directory_path:
            directory_path = "pmc"  # Default fallback
            print_status(f"No path provided, using default: {directory_path}", "info")

    # Validate directory exists
    if not os.path.exists(directory_path):
        print_status(f"Error: Directory not found: {directory_path}", "error")
        return

    if not os.path.isdir(directory_path):
        print_status(f"Error: Path is not a directory: {directory_path}", "error")
        return

    # Create output directory if it doesn't exist
    output_dir = args.output
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print_status(f"Created output directory: {output_dir}", "info")

    # Generate output filename based on directory name
    dir_name = os.path.basename(directory_path.rstrip(os.sep))
    output_file = os.path.join(output_dir, f"{dir_name}_emails.csv")

    # Process all files in the directory
    df_emails = process_directory(directory_path)

    if len(df_emails) > 0:
        # Save to CSV
        df_emails.to_csv(output_file, index=False, encoding='utf-8-sig')
        print_status(f"Results saved to: {output_file}", "success")

        # Display summary
        print_status("Extraction Summary:", "header")
        print_status(f"Total unique email addresses extracted: {len(df_emails)}", "info")

        # Show first few records
        if len(df_emails) > 0:
            print_status("Sample records:", "info")
            for i, row in df_emails.head(10).iterrows():
                print_status(f"  {row['Author Name']} - {row['Email']}", "info")

            if len(df_emails) > 10:
                print_status(f"  ... and {len(df_emails) - 10} more records", "info")
    else:
        print_status("No email addresses found in any files", "warning")

if __name__ == "__main__":
    main()
